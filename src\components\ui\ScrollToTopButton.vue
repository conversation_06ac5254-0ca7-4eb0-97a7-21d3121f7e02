<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { Button } from "@/components/ui/button";
import { ChevronUp } from "lucide-vue-next";

const isVisible = ref(false);
const scrollThreshold = 300; // Show button after scrolling 300px

const checkScrollPosition = () => {
    isVisible.value = window.scrollY > scrollThreshold;
};

const scrollToTop = () => {
    window.scrollTo({
        top: 0,
        behavior: "smooth",
    });
};

onMounted(() => {
    window.addEventListener("scroll", checkScrollPosition);
    checkScrollPosition(); // Check initial position
});

onUnmounted(() => {
    window.removeEventListener("scroll", checkScrollPosition);
});
</script>

<template>
    <Transition
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="opacity-0 translate-y-2 scale-95"
        enter-to-class="opacity-100 translate-y-0 scale-100"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 translate-y-0 scale-100"
        leave-to-class="opacity-0 translate-y-2 scale-95"
    >
        <Button
            v-if="isVisible"
            @click="scrollToTop"
            variant="default"
            size="icon"
            class="fixed bottom-6 right-6 z-40 bg-sky-600 hover:bg-sky-700 text-white shadow-lg hover:shadow-xl focus:ring-2 focus:ring-sky-600 focus:ring-offset-2 rounded-full"
            aria-label="Scroll to top of page"
        >
            <ChevronUp class="h-5 w-5" />
        </Button>
    </Transition>
</template>
