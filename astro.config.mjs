// @ts-check

import tailwindcss from "@tailwindcss/vite";
import { defineConfig, fontProviders } from "astro/config";

import vue from "@astrojs/vue";

// https://astro.build/config
export default defineConfig({
    vite: {
        plugins: [tailwindcss()],
    },

    experimental: {
        fonts: [
            {
                name: "Courier Prime",
                cssVariable: "--font-courier-prime",
                provider: fontProviders.google(),
                weights: [400, 700],
                styles: ["normal", "italic"],
                subsets: ["latin"],
                fallbacks: ["Courier New", "ui-monospace", "monospace"],
            },
        ],
    },

    integrations: [vue()],
});
