# Testing Migration Plan

This repository currently has no automated tests, linters, or formatters configured. To satisfy the testing policy and provide reliable quality gates, the following stack is recommended for an Astro + Tailwind site:

## Recommended Tooling

- Unit testing: Vitest
- Component testing (DOM): @testing-library/dom
- E2E testing: Playwright
- Linting: ESLint (astro + typescript) and Prettier
- CI: GitHub Actions

## Implementation Steps (Estimate ~1 day)

1. Add ESLint and Prettier with Astro plugin; configure lint scripts
2. Add Vitest and DOM Testing Library; add basic smoke tests for index.astro
3. Add Playwright; script to run headed and headless; create navigation and a11y smoke tests
4. Add GitHub Actions workflow to run lint, unit, and e2e tests on PRs

## Risks

- Additional dependencies; longer CI times
- Flaky E2E if network resources are introduced (keep assets local)

## Temporary Procedure

Until tests are added, please perform the manual checks in TESTING_GUIDE.md for each change and ensure `pnpm build` succeeds locally (requires `sharp`).

