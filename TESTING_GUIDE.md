# Testing Guide

This project is an Astro + TailwindCSS portfolio landing page for <PERSON><PERSON><PERSON>. This guide explains how to validate functionality, responsiveness, accessibility, and performance.

## 🧭 Manual Testing Procedures

- Browser smoke test: build then preview, verify all sections render without layout shift
- Screen sizes: 360px, 768px, 1024px, 1440px
- Keyboard navigation: Tab through header links, skip link, section content
- Color contrast: Ensure text maintains ≥ 4.5:1 contrast ratio
- Images: Placeholder assets load with correct alt text
- Links: In-page anchors navigate correctly (Home, Publications, Experience, Software Screens)

## 🔬 Unit/Component Testing

- Currently not configured. Recommendation: Vitest + @testing-library/dom for component-level tests on generated HTML structure. See TESTING_MIGRATION.md for setup plan.

## 🧪 End-to-End Testing

- Recommendation: Playwright for critical journeys (navigation, skip link, section visibility). See TESTING_MIGRATION.md for an implementation plan and sample specs.

## 🧼 Code Quality Standards

- TypeScript: Astro strict config is enabled
- Linting/Formatting: No ESLint/Prettier yet (see migration doc)

## 🛠 Execution Commands

```bash
# Build the site (requires sharp for astro:assets image optimization)
pnpm run build

# Preview the production build
pnpm run preview

# Astro diagnostics
pnpm run astro -- check
```

## 🔧 Troubleshooting

- MissingSharp error during build: install sharp (requires maintainer approval)
  - pnpm add sharp
- If you cannot install sharp, switch to the passthrough image service (not recommended here because optimized images are a requirement).

