---
import Layout from '@/layouts/main.astro';
import Header from '@/components/Header.astro';
import Hero from '@/components/Hero.astro';
import PublicationsWithFilter from '@/components/PublicationsWithFilter.vue';
import Experience from '@/components/Experience.astro';
import SoftwareScreens from '@/components/SoftwareScreens.astro';
import Footer from '@/components/Footer.astro';
import {
  personalInfo,
  researchFields,
  publications,
  employment,
  education,
  certifications,
  softwareScreens,
  navigation,
  siteInfo
} from '@/data/portfolio';
---

<Layout
  title={siteInfo.title}
  bodyClass="bg-[#f3f6fb] text-slate-800"
>
  <Header
    brandName={siteInfo.brandName}
    navigation={navigation}
    skipToContentText={siteInfo.skipToContentText}
  />

  <main id="main" class="mx-auto max-w-6xl px-4 sm:px-6">
    <Hero
      personalInfo={personalInfo}
      researchFields={researchFields}
    />

    <PublicationsWithFilter publications={publications} client:load />

    <Experience
      employment={employment}
      education={education}
      certifications={certifications}
    />

    <SoftwareScreens softwareScreens={softwareScreens} />
  </main>

  <Footer
    personalName={personalInfo.name}
    footerText={siteInfo.footerText}
  />
</Layout>
