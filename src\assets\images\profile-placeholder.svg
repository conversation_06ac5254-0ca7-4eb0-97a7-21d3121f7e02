<svg xmlns="http://www.w3.org/2000/svg" width="600" height="600" viewBox="0 0 600 600" role="img" aria-labelledby="title desc">
  <title id="title">Profile Picture Placeholder</title>
  <desc id="desc">Circular placeholder representing a headshot. Replace with <PERSON><PERSON><PERSON>'s profile photo.</desc>
  <defs>
    <linearGradient id="bg" x1="0" x2="0" y1="0" y2="1">
      <stop offset="0%" stop-color="#eef3f8"/>
      <stop offset="100%" stop-color="#dde6f1"/>
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#bg)"/>
  <circle cx="300" cy="220" r="120" fill="#c7d6ea" stroke="#1d4ed8" stroke-width="8"/>
  <rect x="130" y="360" width="340" height="140" rx="70" fill="#c7d6ea" stroke="#1d4ed8" stroke-width="8"/>
  <text x="300" y="335" text-anchor="middle" font-family="Courier New, ui-monospace, monospace" font-size="22" fill="#1e3a8a">Profile Picture Placeholder</text>
</svg>

