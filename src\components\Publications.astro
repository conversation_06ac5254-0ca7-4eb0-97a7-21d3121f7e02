---
import { Image } from 'astro:assets';
import type { Publication } from '@/data/portfolio';
import publicationThumb from '@/assets/images/publication-thumb.svg';

interface Props {
  publications: Publication[];
}

const { publications } = Astro.props;

const publishedPapers = publications.filter(pub => pub.type === 'published');
const unpublishedPapers = publications.filter(pub => pub.type === 'unpublished');
---

<section id="publications" aria-labelledby="pub-title" class="py-10 border-t border-slate-200">
  <!-- Section Header -->
  <div class="border-l-4 border-sky-600 pl-4 mb-8">
    <h2 id="pub-title" class="text-2xl text-slate-700 font-normal">Academic Peer-Reviewed Publications</h2>
  </div>

  <!-- Filter Buttons -->
  <div class="mb-6">
    <p class="text-sm text-slate-500 mb-3">Filter:</p>
    <div class="flex gap-2 mb-4">
      <button class="px-4 py-2 bg-sky-600 text-white rounded text-sm font-medium">Published</button>
      <button class="px-4 py-2 bg-slate-300 text-slate-700 rounded text-sm font-medium">Unpublished</button>
    </div>
    <p class="text-sm text-slate-400">Showing published and unpublished papers.</p>
  </div>

  <!-- Published Papers -->
  {publishedPapers.length > 0 && (
    <div class="mb-10">
      <div class="flex items-center gap-2 mb-6">
        <div class="w-3 h-3 bg-sky-600 rounded-full"></div>
        <h3 class="text-lg text-slate-700 font-medium">Published</h3>
      </div>

      <div class="space-y-6">
        {publishedPapers.map((publication: Publication, index: number) => (
          <article class="flex gap-4 p-4 bg-white border border-slate-200 rounded-lg shadow-sm">
            <!-- Index Number -->
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-sky-50 border border-sky-200 rounded-full flex items-center justify-center">
                <span class="text-sky-700 font-bold text-sm">{String(index + 1).padStart(2, '0')}</span>
              </div>
            </div>

            <!-- Thumbnail -->
            <div class="flex-shrink-0 w-16 h-16 bg-slate-100 rounded border overflow-hidden flex items-center justify-center">
              <Image src={publicationThumb} width={64} height={64} alt="Publication thumbnail" class="w-full h-full object-cover" />
            </div>

            <!-- Content -->
            <div class="flex-1 min-w-0">
              <p class="text-sm text-slate-500 mb-1" set:html={publication.authorsHtml || publication.authors}></p>
              <h4 class="text-slate-800 font-semibold mb-1 leading-tight" set:html={publication.titleHtml || publication.title}></h4>
              <p class="text-sm text-slate-600 mb-2" set:html={publication.journalHtml || publication.journal}></p>

              <div class="flex items-center gap-4 text-xs">
                <span class="px-2 py-1 bg-slate-100 text-slate-600 rounded">
                  {publication.notesHtml ? (
                    <span set:html={publication.notesHtml}></span>
                  ) : (
                    publication.notes
                  )}
                </span>
                {publication.furtherInfoLink && (
                  <a href={publication.furtherInfoLink} class="text-sky-600 hover:text-sky-800 underline">
                    {publication.furtherInfoText || "further information"}
                  </a>
                )}
              </div>
            </div>
          </article>
        ))}
      </div>
    </div>
  )}

  <!-- Unpublished Papers -->
  {unpublishedPapers.length > 0 && (
    <div>
      <div class="flex items-center gap-2 mb-6">
        <div class="w-3 h-3 bg-amber-500 rounded-full"></div>
        <h3 class="text-lg text-slate-700 font-medium">Unpublished / Working Papers</h3>
      </div>

      <div class="space-y-6">
        {unpublishedPapers.map((publication: Publication, index: number) => (
          <article class="flex gap-4 p-4 bg-white border border-slate-200 rounded-lg shadow-sm">
            <!-- Index Number -->
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-amber-50 border border-amber-200 rounded-full flex items-center justify-center">
                <span class="text-amber-700 font-bold text-sm">{String(index + 1).padStart(2, '0')}</span>
              </div>
            </div>

            <!-- Thumbnail -->
            <div class="flex-shrink-0 w-16 h-16 bg-slate-100 rounded border overflow-hidden flex items-center justify-center">
              <Image src={publicationThumb} width={64} height={64} alt="Working paper thumbnail" class="w-full h-full object-cover" />
            </div>

            <!-- Content -->
            <div class="flex-1 min-w-0">
              <p class="text-sm text-slate-500 mb-1" set:html={publication.authorsHtml || publication.authors}></p>
              <h4 class="text-slate-800 font-semibold mb-1 leading-tight" set:html={publication.titleHtml || publication.title}></h4>
              <p class="text-sm text-slate-600 mb-2" set:html={publication.journalHtml || publication.journal}></p>

              <div class="flex items-center gap-4 text-xs">
                <span class="px-2 py-1 bg-slate-100 text-slate-600 rounded">
                  {publication.notesHtml ? (
                    <span set:html={publication.notesHtml}></span>
                  ) : (
                    publication.notes
                  )}
                </span>
                {publication.furtherInfoLink && (
                  <a href={publication.furtherInfoLink} class="text-sky-600 hover:text-sky-800 underline">
                    {publication.furtherInfoText || "further information"}
                  </a>
                )}
              </div>
            </div>
          </article>
        ))}
      </div>
    </div>
  )}
</section>
