<script setup lang="ts">
import type { Publication } from "@/data/portfolio";

interface Props {
    publication: Publication;
    index: number;
    type: "published" | "unpublished";
}

const props = defineProps<Props>();

// Define styling based on publication type
const typeStyles = {
    published: {
        indexBg: "bg-sky-50",
        indexBorder: "border-sky-200",
        indexText: "text-sky-700",
        mobileOverlay: "bg-sky-600",
    },
    unpublished: {
        indexBg: "bg-amber-50",
        indexBorder: "border-amber-200",
        indexText: "text-amber-700",
        mobileOverlay: "bg-amber-600",
    },
};

const currentStyles = typeStyles[props.type];
const formattedIndex = String(props.index + 1).padStart(2, "0");
const altText = props.type === "published" ? "Publication thumbnail" : "Working paper thumbnail";
</script>

<template>
    <article class="flex flex-col sm:flex-row gap-4 items-start">
        <!-- Left Side: Index Number and Thumbnail (outside card) -->
        <div
            class="flex-shrink-0 flex sm:flex-col flex-row items-center gap-3 w-full sm:w-auto justify-center sm:justify-start"
        >
            <!-- Index Number (hidden on mobile, shown on larger screens) -->
            <div
                :class="[
                    'hidden sm:flex w-12 h-12 rounded-full items-center justify-center',
                    currentStyles.indexBg,
                    currentStyles.indexBorder,
                    'border'
                ]"
            >
                <span :class="['font-bold text-sm', currentStyles.indexText]">
                    {{ formattedIndex }}
                </span>
            </div>

            <!-- Thumbnail -->
            <div
                class="relative w-16 h-16 bg-slate-100 rounded border overflow-hidden flex items-center justify-center flex-shrink-0"
            >
                <img
                    :src="publication.thumbnail"
                    width="64"
                    height="64"
                    :alt="altText"
                    class="w-full h-full object-cover"
                />
                <!-- Mobile: Index number overlay on thumbnail -->
                <div
                    :class="[
                        'absolute top-1 left-1 w-6 h-6 text-white rounded-full flex items-center justify-center text-xs font-bold sm:hidden shadow-sm',
                        currentStyles.mobileOverlay
                    ]"
                >
                    {{ formattedIndex }}
                </div>
            </div>
        </div>

        <!-- Right Side: Content Card -->
        <div
            class="flex-1 min-w-0 bg-white border border-slate-200 rounded-lg shadow-sm p-3 sm:p-4 w-full sm:w-auto"
        >
            <p
                class="text-sm text-slate-500 mb-1 break-words"
                v-html="publication.authorsHtml || publication.authors"
            ></p>
            <h4
                class="text-slate-800 font-semibold mb-1 leading-tight break-words"
                v-html="publication.titleHtml || publication.title"
            ></h4>
            <p
                class="text-sm text-slate-600 mb-2 break-words"
                v-html="publication.journalHtml || publication.journal"
            ></p>

            <div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs">
                <span class="px-2 py-1 bg-slate-100 text-slate-600 rounded break-words">
                    <span v-if="publication.notesHtml" v-html="publication.notesHtml"></span>
                    <span v-else>{{ publication.notes }}</span>
                </span>
                <a
                    v-if="publication.furtherInfoLink"
                    :href="publication.furtherInfoLink"
                    class="text-sky-600 hover:text-sky-800 underline break-words"
                >
                    {{ publication.furtherInfoText || "further information" }}
                </a>
            </div>
        </div>
    </article>
</template>
